# Firebase Service Account File Guide

## 🤔 **Why Do You Need This File?**

The Firebase service account file is **essential** for server-side Firebase operations. Without it, your backend **cannot** send push notifications.

### **What Firebase Service Account Does:**
- 🔐 **Authenticates** your backend server with Firebase
- 🚀 **Enables** sending push notifications from server
- 🎯 **Authorizes** access to Firebase Cloud Messaging (FCM)
- 🔒 **Secures** communication between your server and Firebase

### **Without This File:**
- ❌ Firebase Admin SDK fails to initialize
- ❌ All push notifications fail (0 sent, X failed)
- ❌ Backend cannot authenticate with Firebase servers
- ❌ FCM operations return authentication errors

## 📁 **What's in the File?**

The Firebase service account JSON file contains:
```json
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## 🔍 **Check if You Have the File**

### **Production Diagnostic (Admin Only):**
```bash
curl -X GET https://api.hlenergy.pt/api/v1/push/debug \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

This will show:
- ✅ If Firebase service account file exists
- ✅ If Firebase is initialized
- ✅ FCM subscription statistics
- ❌ Any Firebase errors

### **Expected Response (Working):**
```json
{
  "success": true,
  "data": {
    "firebase": {
      "initialized": true,
      "hasMessaging": true,
      "serviceAccountExists": true,
      "serviceAccountPath": "firebase-service-account.json"
    },
    "subscriptions": {
      "total": 5,
      "active": 3,
      "byRole": {
        "admin": { "total": 2, "active": 2 },
        "user": { "total": 3, "active": 1 }
      }
    }
  }
}
```

### **Expected Response (Broken):**
```json
{
  "success": true,
  "data": {
    "firebase": {
      "initialized": false,
      "hasMessaging": false,
      "serviceAccountExists": false,
      "error": "Could not load the default credentials"
    }
  }
}
```

## 🛠️ **How to Fix Missing Service Account**

### **Step 1: Check if File Exists in Production**
```bash
# SSH to production server
ssh user@your-production-server

# Check for Firebase service account files
ls -la /path/to/backend/firebase-service-account.json
ls -la /path/to/backend/hlenergy-notifications-firebase-adminsdk-*.json
```

### **Step 2: Copy File to Production (if missing)**
```bash
# From your local machine, copy to production
scp hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json user@production:/path/to/backend/

# Or create a symlink with the expected name
ln -s hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json firebase-service-account.json
```

### **Step 3: Set Correct Permissions**
```bash
# Make sure Node.js can read the file
chmod 644 firebase-service-account.json
chown nodejs:nodejs firebase-service-account.json  # or whatever user runs your app
```

### **Step 4: Restart Application**
```bash
# Restart your Node.js application
pm2 restart all
# or
systemctl restart your-app-service
```

### **Step 5: Test the Fix**
```bash
# Test the debug endpoint again
curl -X GET https://api.hlenergy.pt/api/v1/push/debug \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# Should now show: "serviceAccountExists": true, "initialized": true
```

## 🚨 **Common Issues**

### **Issue 1: File Not Found**
**Error**: `"serviceAccountExists": false`
**Solution**: Copy the Firebase service account file to production

### **Issue 2: Permission Denied**
**Error**: `"error": "EACCES: permission denied"`
**Solution**: Fix file permissions with `chmod 644`

### **Issue 3: Invalid JSON**
**Error**: `"error": "Unexpected token"`
**Solution**: Re-download the service account file from Firebase Console

### **Issue 4: Wrong Project**
**Error**: `"error": "Project ID mismatch"`
**Solution**: Ensure the file is for project `hlenergy-notifications`

## 📋 **Quick Checklist**

- [ ] Firebase service account file exists in production
- [ ] File has correct permissions (644)
- [ ] File contains valid JSON
- [ ] Project ID is `hlenergy-notifications`
- [ ] Application restarted after file deployment
- [ ] Debug endpoint shows `"initialized": true`
- [ ] Push notifications now work (`sent > 0`)

## 🎯 **The Bottom Line**

**Without the Firebase service account file, your backend cannot send push notifications at all.**

It's like trying to send an email without login credentials - Firebase needs to know who you are and that you're authorized to send notifications for your project.

The file is your backend's "passport" to Firebase services! 🔐
