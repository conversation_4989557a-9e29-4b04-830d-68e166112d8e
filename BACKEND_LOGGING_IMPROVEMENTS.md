# Backend Logging Improvements

## 🎯 **Issues Fixed**

### **1. IP Address Issue**
- **Problem**: IP always showed `::ffff:127.0.0.1` (IPv6-mapped IPv4 localhost)
- **Solution**: 
  - Added `app.set('trust proxy', true)` to properly handle proxy headers
  - Implemented real IP detection that checks multiple headers:
    - `x-forwarded-for` (most common proxy header)
    - `x-real-ip` (nginx real IP)
    - `connection.remoteAddress`
    - `socket.remoteAddress`
    - Falls back to `req.ip`

### **2. Request ID Issues**
- **Problem**: Request IDs were null or missing
- **Solution**: 
  - Enhanced request ID generation with timestamp, process ID, and random part
  - Format: `REQ_{timestamp}_{processId}_{randomPart}`
  - Added to response headers for easier debugging
  - Ensured request ID is available throughout the request lifecycle

### **3. Null Values in Logs**
- **Problem**: Method, URL, status code, response time, and error stack were null
- **Solution**:
  - Added fallback values for all log fields
  - Enhanced error handling with proper stack trace capture
  - Improved request context capture with additional useful fields

### **4. Enhanced Error Logging**
- **Problem**: Error stack traces were missing or incomplete
- **Solution**:
  - Better error object handling with fallbacks
  - Added error codes and status codes
  - Enhanced request context in error logs
  - Sensitive data filtering in error logs

## 🚀 **New Features Added**

### **1. Enhanced Request Logging**
```javascript
// Now captures:
{
  type: 'request',
  method: 'POST',
  url: '/api/v1/auth/login',
  ip: '*************',           // Real IP, not ::ffff:127.0.0.1
  userAgent: 'Mozilla/5.0...',
  statusCode: 200,
  responseTime: '45ms',
  contentLength: '1024',
  apiVersion: 'v1',
  userId: 123,
  requestId: 'REQ_1705234567890_1a2b3c_xyz789',
  referer: 'https://hlenergy.pt/login',
  origin: 'https://hlenergy.pt',
  host: 'api.hlenergy.pt',
  protocol: 'https',
  httpVersion: '1.1',
  requestBody: { email: '<EMAIL>' }, // Sensitive fields removed
  queryParams: { redirect: '/dashboard' },
  routeParams: { id: '123' }
}
```

### **2. Enhanced Morgan Format**
```bash
# Before: Basic format
POST /api/v1/auth/login 200 1024 - 45.123 ms

# After: Enhanced format with real IP, user ID, and request ID
************* 123 [REQ_1705234567890_1a2b3c_xyz789] POST /api/v1/auth/login 200 1024 - 45.123 ms
```

### **3. Security Event Logging**
- Suspicious pattern detection (XSS, SQL injection, directory traversal)
- Failed login attempt tracking
- High request rate detection (DoS protection)
- All with real IP addresses and request IDs

### **4. Performance Monitoring**
- Slow request detection (>1 second)
- Response time tracking
- Performance metrics logging

### **5. Test Endpoints** (Development Only)
- `GET /api/v1/test-logging/info` - Test info logging
- `GET /api/v1/test-logging/error` - Test error logging
- `POST /api/v1/test-logging/security` - Test security logging
- `GET /api/v1/test-logging/performance` - Test performance logging
- `GET /api/v1/test-logging/request-info` - Debug request information

## 📊 **Log Structure Examples**

### **HTTP Request Log**
```json
{
  "timestamp": "2025-01-14 15:30:45.123",
  "level": "http",
  "message": "POST /api/v1/auth/login 200 - 45ms",
  "type": "request",
  "method": "POST",
  "url": "/api/v1/auth/login",
  "ip": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "statusCode": 200,
  "responseTime": "45ms",
  "requestId": "REQ_1705234567890_1a2b3c_xyz789",
  "userId": 123,
  "apiVersion": "v1"
}
```

### **Error Log**
```json
{
  "timestamp": "2025-01-14 15:30:45.123",
  "level": "error",
  "message": "Application Error",
  "type": "error",
  "name": "ValidationError",
  "message": "Email is required",
  "stack": "ValidationError: Email is required\n    at validateUser...",
  "code": "VALIDATION_ERROR",
  "statusCode": 400,
  "request": {
    "method": "POST",
    "url": "/api/v1/auth/register",
    "ip": "*************",
    "userAgent": "Mozilla/5.0...",
    "requestId": "REQ_1705234567890_1a2b3c_xyz789",
    "body": { "name": "John Doe" }
  }
}
```

### **Security Event Log**
```json
{
  "timestamp": "2025-01-14 15:30:45.123",
  "level": "warn",
  "message": "Security Event",
  "type": "security",
  "event": "Suspicious Request Pattern",
  "pattern": "/<script/i",
  "method": "POST",
  "url": "/api/v1/contact",
  "ip": "*************",
  "requestId": "REQ_1705234567890_1a2b3c_xyz789"
}
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Logging level
LOG_LEVEL=info

# Trust proxy (important for production)
TRUST_PROXY=true

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### **Production Considerations**
- Real IP detection works with nginx, Apache, Cloudflare, and other proxies
- Request IDs are unique across multiple server instances
- Sensitive data is automatically filtered from logs
- Performance impact is minimal due to efficient logging

## 🧪 **Testing the Improvements**

1. **Start the backend server**
2. **Make a test request**:
   ```bash
   curl -X GET http://localhost:3001/api/v1/test-logging/request-info
   ```
3. **Check the logs** - you should now see:
   - Real IP addresses (not ::ffff:127.0.0.1)
   - Proper request IDs
   - Complete request information
   - No null values

## 📈 **Benefits**

- ✅ **Accurate IP tracking** for security and analytics
- ✅ **Request tracing** with unique IDs across the entire request lifecycle
- ✅ **Complete error context** for faster debugging
- ✅ **Security monitoring** with real IP addresses
- ✅ **Performance insights** with detailed timing information
- ✅ **Production-ready** logging that works behind proxies and load balancers

The logging system now provides comprehensive, accurate, and actionable information for monitoring, debugging, and security analysis! 🎉
