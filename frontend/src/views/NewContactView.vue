<template>
  <div>
    <!-- Hero Section -->
    <div class="hero min-h-96 bg-base-200">
      <div class="hero-content text-center">
        <div class="max-w-2xl">
          <h1 class="text-5xl font-bold text-base-content mb-6">Contact Us</h1>
          <p class="text-lg text-base-content/70">
            Ready to optimize your energy consumption? Get in touch with our experts for a free consultation.
          </p>
        </div>
      </div>
    </div>

    <!-- Contact Form & Info -->
    <div class="py-20 bg-base-100">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Contact Form -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <h2 class="card-title text-2xl mb-6">Send us a message</h2>
              
              <form @submit.prevent="submitForm" class="space-y-4">
                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Name *</span>
                  </label>
                  <input 
                    type="text" 
                    v-model="form.name"
                    placeholder="Your full name" 
                    class="input input-bordered w-full" 
                    required 
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Email *</span>
                  </label>
                  <input 
                    type="email" 
                    v-model="form.email"
                    placeholder="<EMAIL>" 
                    class="input input-bordered w-full" 
                    required 
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Phone</span>
                  </label>
                  <input 
                    type="tel" 
                    v-model="form.phone"
                    placeholder="+351 123 456 789" 
                    class="input input-bordered w-full" 
                  />
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Service Type</span>
                  </label>
                  <select v-model="form.serviceType" class="select select-bordered w-full">
                    <option value="">Select a service</option>
                    <option value="business">Business Energy Audit</option>
                    <option value="residential">Home Energy Assessment</option>
                    <option value="consultation">General Consultation</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div class="form-control">
                  <label class="label">
                    <span class="label-text">Message *</span>
                  </label>
                  <textarea 
                    v-model="form.message"
                    class="textarea textarea-bordered h-32" 
                    placeholder="Tell us about your energy needs..."
                    required
                  ></textarea>
                </div>

                <div class="form-control">
                  <label class="cursor-pointer label justify-start gap-3">
                    <input type="checkbox" v-model="form.consent" class="checkbox checkbox-primary" required />
                    <span class="label-text">I agree to be contacted regarding my inquiry *</span>
                  </label>
                </div>

                <div class="card-actions justify-end">
                  <button 
                    type="submit" 
                    class="btn btn-primary btn-wide"
                    :class="{ 'loading': isSubmitting }"
                    :disabled="isSubmitting"
                  >
                    <span v-if="!isSubmitting">Send Message</span>
                    <span v-else>Sending...</span>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <!-- Contact Details -->
            <div class="card bg-base-200 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-xl mb-4">Get in Touch</h3>
                
                <div class="space-y-4">
                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold">Phone</p>
                      <a href="tel:+351123456789" class="text-primary hover:underline">+351 123 456 789</a>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-secondary/20 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold">Email</p>
                      <a href="mailto:<EMAIL>" class="text-secondary hover:underline"><EMAIL></a>
                    </div>
                  </div>

                  <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center">
                      <svg class="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold">Address</p>
                      <p class="text-base-content/70">Lisbon, Portugal</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Hours -->
            <div class="card bg-base-200 shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-xl mb-4">Business Hours</h3>
                
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span>Monday - Friday</span>
                    <span class="font-semibold">9:00 AM - 6:00 PM</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Saturday</span>
                    <span class="font-semibold">10:00 AM - 2:00 PM</span>
                  </div>
                  <div class="flex justify-between">
                    <span>Sunday</span>
                    <span class="text-base-content/50">Closed</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="card bg-primary text-primary-content shadow-xl">
              <div class="card-body">
                <h3 class="card-title text-xl mb-4">Need Immediate Help?</h3>
                <p class="mb-4 opacity-90">
                  For urgent energy consultations or emergency support, contact us directly.
                </p>
                <div class="card-actions">
                  <a href="tel:+351123456789" class="btn btn-accent">Call Now</a>
                  <a href="https://wa.me/351123456789" target="_blank" class="btn btn-outline btn-accent">WhatsApp</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// Form data
const form = reactive({
  name: '',
  email: '',
  phone: '',
  serviceType: '',
  message: '',
  consent: false
})

const isSubmitting = ref(false)

// Form submission
const submitForm = async () => {
  isSubmitting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Show success message (you can implement toast notifications here)
    alert('Message sent successfully! We will get back to you soon.')
    
    // Reset form
    Object.keys(form).forEach(key => {
      if (typeof form[key] === 'boolean') {
        form[key] = false
      } else {
        form[key] = ''
      }
    })
  } catch (error) {
    alert('Failed to send message. Please try again.')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* Form animations */
.form-control {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card hover effects */
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
}
</style>
