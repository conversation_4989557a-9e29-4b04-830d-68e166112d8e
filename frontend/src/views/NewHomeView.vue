<template>
  <div>
    <!-- Hero Section -->
    <div class="hero min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10">
      <div class="hero-content text-center">
        <div class="max-w-4xl">
          <h1 class="text-6xl font-bold text-base-content mb-6">
            Welcome to <span class="text-primary">HLenergy</span>
          </h1>
          <p class="text-xl text-base-content/80 mb-8 max-w-2xl mx-auto">
            Your trusted partner for comprehensive energy solutions. We provide expert consultation 
            for both businesses and consumers, helping you optimize energy efficiency and reduce costs.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <router-link to="/new-contact" class="btn btn-primary btn-lg">
              Get Free Assessment
            </router-link>
            <router-link to="/services" class="btn btn-outline btn-lg">
              Our Services
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Services Preview -->
    <div class="py-20 bg-base-100">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-base-content mb-4">Our Services</h2>
          <p class="text-lg text-base-content/70 max-w-2xl mx-auto">
            Comprehensive energy solutions tailored to your needs
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- B2B Services -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <h3 class="card-title text-primary">Business Solutions</h3>
              <p class="text-base-content/70">
                Energy audits, efficiency optimization, and cost reduction strategies for businesses of all sizes.
              </p>
              <div class="card-actions justify-end">
                <router-link to="/services" class="btn btn-primary btn-sm">Learn More</router-link>
              </div>
            </div>
          </div>

          <!-- B2C Services -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-secondary/20 rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
              </div>
              <h3 class="card-title text-secondary">Home Energy</h3>
              <p class="text-base-content/70">
                Residential energy assessments, smart home solutions, and renewable energy consulting.
              </p>
              <div class="card-actions justify-end">
                <router-link to="/services" class="btn btn-secondary btn-sm">Learn More</router-link>
              </div>
            </div>
          </div>

          <!-- Consultation -->
          <div class="card bg-base-200 shadow-xl">
            <div class="card-body">
              <div class="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center mb-4">
                <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <h3 class="card-title text-accent">Expert Consultation</h3>
              <p class="text-base-content/70">
                Professional energy consulting with personalized recommendations and implementation support.
              </p>
              <div class="card-actions justify-end">
                <router-link to="/new-contact" class="btn btn-accent btn-sm">Get Started</router-link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="py-20 bg-base-200">
      <div class="container mx-auto px-4">
        <div class="stats stats-vertical lg:stats-horizontal shadow-xl w-full">
          <div class="stat">
            <div class="stat-figure text-primary">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
            <div class="stat-title">Projects Completed</div>
            <div class="stat-value text-primary">500+</div>
            <div class="stat-desc">Energy assessments delivered</div>
          </div>

          <div class="stat">
            <div class="stat-figure text-secondary">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="stat-title">Average Savings</div>
            <div class="stat-value text-secondary">25%</div>
            <div class="stat-desc">Reduction in energy costs</div>
          </div>

          <div class="stat">
            <div class="stat-figure text-accent">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
            <div class="stat-title">Happy Clients</div>
            <div class="stat-value text-accent">98%</div>
            <div class="stat-desc">Customer satisfaction rate</div>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="py-20 bg-primary text-primary-content">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-4xl font-bold mb-6">Ready to Optimize Your Energy?</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto opacity-90">
          Get a free energy assessment and discover how much you can save with our expert solutions.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link to="/new-contact" class="btn btn-accent btn-lg">
            Schedule Free Assessment
          </router-link>
          <a href="tel:+351123456789" class="btn btn-outline btn-lg text-primary-content border-primary-content hover:bg-primary-content hover:text-primary">
            Call Now
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// New home page with DaisyUI v5 components
</script>

<style scoped>
/* Custom gradient background */
.hero {
  background: linear-gradient(135deg, 
    oklch(var(--color-primary) / 0.1) 0%, 
    oklch(var(--color-secondary) / 0.1) 100%);
}

/* Smooth animations */
.card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 10px 10px -5px rgb(0 0 0 / 0.04);
}

/* Stats animation */
.stats {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
