<template>
  <div class="min-h-screen bg-base-100">
    <!-- Navigation -->
    <div class="navbar bg-base-200 shadow-lg">
      <div class="navbar-start">
        <div class="dropdown">
          <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"></path>
            </svg>
          </div>
          <ul tabindex="0" class="menu menu-sm dropdown-content bg-base-100 rounded-box z-[1] mt-3 w-52 p-2 shadow">
            <li><router-link to="/new-home">Home</router-link></li>
            <li><router-link to="/about">About</router-link></li>
            <li><router-link to="/services">Services</router-link></li>
            <li><router-link to="/new-contact">Contact</router-link></li>
          </ul>
        </div>
        <router-link to="/new-home" class="btn btn-ghost text-xl">
          <img src="/src/assets/hl-energy-logo.png" alt="HLenergy" class="h-8 w-auto" />
          HLenergy
        </router-link>
      </div>
      
      <div class="navbar-center hidden lg:flex">
        <ul class="menu menu-horizontal px-1">
          <li><router-link to="/new-home" class="btn btn-ghost">Home</router-link></li>
          <li><router-link to="/about" class="btn btn-ghost">About</router-link></li>
          <li><router-link to="/services" class="btn btn-ghost">Services</router-link></li>
          <li><router-link to="/new-contact" class="btn btn-ghost">Contact</router-link></li>
        </ul>
      </div>
      
      <div class="navbar-end">
        <!-- Theme Switcher -->
        <div class="dropdown dropdown-end">
          <div tabindex="0" role="button" class="btn btn-ghost m-1">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <ul tabindex="0" class="dropdown-content bg-base-300 rounded-box z-[1] w-52 p-2 shadow-2xl">
            <li>
              <input type="radio" name="theme-dropdown" class="theme-controller btn btn-sm btn-block btn-ghost justify-start" aria-label="Light" value="light" />
            </li>
            <li>
              <input type="radio" name="theme-dropdown" class="theme-controller btn btn-sm btn-block btn-ghost justify-start" aria-label="Dark" value="dark" />
            </li>
            <li>
              <input type="radio" name="theme-dropdown" class="theme-controller btn btn-sm btn-block btn-ghost justify-start" aria-label="HLenergy Light" value="hlenergy-light" checked />
            </li>
            <li>
              <input type="radio" name="theme-dropdown" class="theme-controller btn btn-sm btn-block btn-ghost justify-start" aria-label="HLenergy Dark" value="hlenergy-dark" />
            </li>
          </ul>
        </div>
        
        <!-- Login Button -->
        <router-link to="/login" class="btn btn-primary">Login</router-link>
      </div>
    </div>

    <!-- Main Content -->
    <main class="flex-1">
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="footer footer-center bg-base-200 text-base-content p-10">
      <aside>
        <img src="/src/assets/hl-energy-logo.png" alt="HLenergy" class="h-12 w-auto" />
        <p class="font-bold">
          HLenergy
          <br />
          Providing reliable energy solutions since 2020
        </p>
        <p>Copyright © 2024 - All rights reserved</p>
      </aside>
      <nav>
        <div class="grid grid-flow-col gap-4">
          <a href="https://facebook.com/share/1ArWY9phE5/?mibextid=wwxifr" target="_blank" class="btn btn-ghost btn-circle">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </a>
          <a href="https://www.instagram.com/hl_energy_" target="_blank" class="btn btn-ghost btn-circle">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987c6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.611-3.132-1.551c-.684-.94-.684-2.102 0-3.042c.684-.94 1.835-1.551 3.132-1.551s2.448.611 3.132 1.551c.684.94.684 2.102 0 3.042c-.684.94-1.835 1.551-3.132 1.551zm7.718 0c-1.297 0-2.448-.611-3.132-1.551c-.684-.94-.684-2.102 0-3.042c.684-.94 1.835-1.551 3.132-1.551s2.448.611 3.132 1.551c.684.94.684 2.102 0 3.042c-.684.94-1.835 1.551-3.132 1.551z"/>
            </svg>
          </a>
        </div>
      </nav>
    </footer>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  // Set default theme if none is set
  if (!document.documentElement.getAttribute('data-theme')) {
    document.documentElement.setAttribute('data-theme', 'hlenergy-light')
  }
})
</script>

<style scoped>
/* Router link active states */
.router-link-active {
  @apply text-primary font-semibold;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Ensure proper spacing */
.navbar {
  min-height: 4rem;
}

.footer {
  margin-top: auto;
}
</style>
