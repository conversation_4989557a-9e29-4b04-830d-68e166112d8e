{"name": "frontend", "version": "2.1.3", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "build": "npm run build-only", "build:staging": "vite build --mode staging", "build:production": "vite build --mode production", "build:with-types": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/", "version:patch": "node scripts/bump-version.cjs patch", "version:minor": "node scripts/bump-version.cjs minor", "version:major": "node scripts/bump-version.cjs major", "version:custom": "node scripts/bump-version.cjs custom", "version:current": "node -e \"console.log(require('./package.json').version)\"", "version:check": "node scripts/check-version.cjs", "analyze:performance": "node ../scripts/performance-analysis.js", "test:socket-performance": "node ../scripts/socket-performance-test.js", "analyze:bundle": "npm run build && npx webpack-bundle-analyzer dist/assets/*.js", "test:memory": "node ../scripts/socket-performance-test.js --connections 50 --duration 30", "lighthouse": "npx lighthouse ${VITE_FRONTEND_BASE_URL:-http://localhost:5173} --output=html --output-path=lighthouse-report.html", "lighthouse:staging": "npx lighthouse https://staging.hlenergy.pt --output=html --output-path=lighthouse-staging-report.html", "lighthouse:production": "npx lighthouse https://hlenergy.pt --output=html --output-path=lighthouse-production-report.html", "update-deps": "chmod +x scripts/update-dependencies.sh && ./scripts/update-dependencies.sh", "update-libs-safe": "node scripts/update-libraries-safe.js", "check-outdated": "npm outdated", "update-safe": "npm update", "clean-install": "rm -rf node_modules package-lock.json && npm install"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@vueuse/core": "^13.5.0", "@vueuse/head": "^2.0.0", "axios": "^1.10.0", "firebase": "^12.0.0", "pinia": "^3.0.3", "socket.io-client": "^4.8.1", "vite-plugin-pwa": "^1.0.1", "vue": "^3.5.17", "vue-i18n": "^11.1.10", "vue-recaptcha-v3": "^2.0.1", "vue-router": "^4.5.1", "workbox-window": "^7.3.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4.1.11", "@tsconfig/node22": "^22.0.2", "@types/jsdom": "^21.1.7", "@types/node": "^24.0.15", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vitest/eslint-plugin": "^1.3.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autocannon": "^8.0.0", "autoprefixer": "^10.4.21", "clinic": "^13.0.0", "daisyui": "^5.0.46", "eslint": "^9.31.0", "eslint-plugin-oxlint": "^1.7.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "^10.3.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lighthouse": "^12.8.0", "npm-run-all2": "^8.0.4", "oxlint": "^1.7.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.0.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^3.0.3", "webpack-bundle-analyzer": "^4.10.2"}}