# Firebase Production Troubleshooting Guide

## 🚨 **Issue**: Firebase Works in Dev, Fails in Production

**Symptoms**:
- Local dev: Firebase notifications work fine
- Production: `"sent": 0, "failed": 3` for all Firebase endpoints

## 🔍 **Diagnostic Steps**

### **Step 1: Run Firebase Debug Endpoint**
```bash
curl -X GET https://api.hlenergy.pt/api/v1/test-logging/firebase-debug
```

This will check:
- ✅ Firebase service account file exists and is readable
- ✅ Firebase service can initialize
- ✅ Environment configuration
- ✅ File permissions and paths

### **Step 2: Check FCM Subscriptions**
```bash
curl -X GET https://api.hlenergy.pt/api/v1/test-logging/fcm-subscriptions
```

This will show:
- How many FCM subscriptions exist
- What user roles are stored
- Active vs inactive subscriptions

## 🎯 **Most Common Production Issues**

### **1. Firebase Service Account File Missing**
**Problem**: The Firebase service account JSON file isn't deployed to production.

**Check**: Look for these files in production:
- `/backend/firebase-service-account.json`
- `/backend/hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json`

**Solution**:
```bash
# On production server, check if files exist
ls -la /path/to/backend/firebase-service-account.json
ls -la /path/to/backend/hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json

# Check file permissions
chmod 644 /path/to/backend/firebase-service-account.json
```

### **2. Environment Variables**
**Problem**: Production environment missing Firebase-related variables.

**Check**: Ensure these are set in production:
```bash
# Optional but helpful
GOOGLE_APPLICATION_CREDENTIALS=/path/to/firebase-service-account.json
FIREBASE_PROJECT_ID=hlenergy-notifications
```

### **3. Network/Firewall Issues**
**Problem**: Production server can't reach Firebase servers.

**Test**: From production server:
```bash
# Test connectivity to Firebase
curl -I https://fcm.googleapis.com/
curl -I https://firebase.googleapis.com/

# Check DNS resolution
nslookup fcm.googleapis.com
```

### **4. File Permissions**
**Problem**: Node.js process can't read the Firebase service account file.

**Fix**:
```bash
# Make sure the file is readable by the Node.js process
chmod 644 firebase-service-account.json
chown nodejs:nodejs firebase-service-account.json  # or whatever user runs Node.js
```

### **5. Docker/Container Issues**
**Problem**: If using Docker, the Firebase file might not be copied to the container.

**Check Dockerfile**:
```dockerfile
# Make sure this line exists in Dockerfile
COPY firebase-service-account.json ./
# or
COPY hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json ./
```

## 🔧 **Quick Fixes**

### **Fix 1: Copy Firebase Service Account to Production**
```bash
# Copy the Firebase service account file to production
scp hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json user@production-server:/path/to/backend/

# Or create a symlink if the file has a different name
ln -s hlenergy-notifications-firebase-adminsdk-fbsvc-d0c7e15f92.json firebase-service-account.json
```

### **Fix 2: Set Environment Variables**
```bash
# Add to production environment
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/firebase-service-account.json
export NODE_ENV=production
```

### **Fix 3: Restart Services**
```bash
# Restart the Node.js application
pm2 restart all
# or
systemctl restart your-app-service
```

## 🧪 **Testing After Fixes**

### **Test 1: Firebase Debug**
```bash
curl -X GET https://api.hlenergy.pt/api/v1/test-logging/firebase-debug
```
**Expected**: `"initialized": true`, `"hasMessaging": true`

### **Test 2: User Notification**
```bash
curl -X POST https://api.hlenergy.pt/api/v1/push/send/user/fcm \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "userId": 5,
    "title": "Production Test",
    "body": "Testing Firebase in production"
  }'
```
**Expected**: `"sent": 1` (or more), `"failed": 0`

### **Test 3: Admin Notification**
```bash
curl -X POST https://api.hlenergy.pt/api/v1/push/send/admin/fcm \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "Admin Production Test",
    "body": "Testing admin Firebase in production"
  }'
```
**Expected**: `"sent": 1` (or more), `"failed": 0`

## 📋 **Checklist for Production Deployment**

- [ ] Firebase service account JSON file deployed
- [ ] File has correct permissions (644)
- [ ] File is readable by Node.js process
- [ ] Environment variables set (if needed)
- [ ] Network connectivity to Firebase servers
- [ ] Application restarted after file deployment
- [ ] Debug endpoint shows Firebase initialized
- [ ] FCM subscriptions exist in database
- [ ] Test notifications work

## 🚨 **Emergency Fix**

If you need an immediate fix and can't access the production server:

1. **Check if the Firebase service account file exists in production**
2. **If missing, deploy it immediately**
3. **Restart the Node.js application**
4. **Test with the debug endpoint**

The most likely issue is that the Firebase service account file is missing or not readable in production! 🎯
